# 🔧 Prepočítanie Features s Pln<PERSON> Históriou

Tento balík scriptov rieši problém s chý<PERSON>j<PERSON><PERSON><PERSON> indikátormi (ADX, DMP, DMN, atď.) na začiatku dní tým, že prepočíta všetky features s plnou históriou z predchádzajúcich dní.

## 📋 Problém

**Pôvodný problém:**
- Aprílové dáta: chýbajú ADX_14, DMP_14, DMN_14 indikátory
- Júlov<PERSON> dáta: všetky indikátory prítomné
- Indikátory potrebujú históriu z predchádzajúcich dní pre správny výpočet

**Riešenie:**
- Načítanie všetkých dostupných dát naraz (nie po dňoch)
- Výpočet indikátorov s rozš<PERSON>renou históriou (30 dní lookback)
- Uloženie výsledkov po dňoch

## 🚀 Rýchle Spustenie

### Jednodu<PERSON><PERSON> spôsob (odporúčaný):
```bash
./fix_all_indicators.sh
```

Tento script automaticky:
- Prepočíta features pre oba timeframes (1s a 5m)
- Použije 30 dní lookback histórie
- Spracuje všetky dostupné dáta

## 🔧 Manuálne Spustenie

### 1. Batch prepočítanie (všetky dostupné dáta):
```bash
# Pre oba timeframes
python batch_recompute_features.py --preset both --raw-data-dir parquet_raw --output-dir parquet_processed

# Len pre 1s
python batch_recompute_features.py --preset 1s --raw-data-dir parquet_raw --output-dir parquet_processed

# Len pre 5m  
python batch_recompute_features.py --preset 5m --raw-data-dir parquet_raw --output-dir parquet_processed
```

### 2. Prepočítanie konkrétneho obdobia:
```bash
python recompute_all_features.py \
    --config strategyConfig_scalp_1s_fixed.json \
    --raw-data-dir parquet_raw \
    --output-dir parquet_processed \
    --start 2025-04-01 \
    --end 2025-04-30 \
    --lookback-days 30
```

## 📊 Súbory

### Hlavné scripty:
- **`recompute_all_features.py`** - Základný script pre prepočítanie s históriou
- **`batch_recompute_features.py`** - Batch spracovanie všetkých dostupných dát
- **`fix_all_indicators.sh`** - Jednoduchý wrapper pre rýchle spustenie

### Konfigurácie:
- **`strategyConfig_scalp_1s_fixed.json`** - Pre 1s timeframe
- **`strategyConfig_scalp_5m_fixed.json`** - Pre 5m timeframe

## ⚙️ Parametre

### `recompute_all_features.py`:
- `--config` - Cesta k JSON config súboru
- `--raw-data-dir` - Adresár so surovými dátami (default: parquet_raw)
- `--output-dir` - Výstupný adresár (default: parquet_processed)
- `--start` - Začiatočný dátum (YYYY-MM-DD)
- `--end` - Koncový dátum (YYYY-MM-DD)
- `--lookback-days` - Počet dní histórie (default: 30)

### `batch_recompute_features.py`:
- `--preset` - Prednastavené konfigurácie: `1s`, `5m`, `both`
- `--max-range-days` - Max dní na jedno spustenie (default: 30)
- Ostatné parametre rovnaké ako vyššie

## 🔍 Ako to funguje

1. **Rozšírený rozsah**: Script načíta dáta od `start_date - lookback_days` do `end_date`
2. **Plná história**: Indikátory sa počítajú s kompletnou históriou
3. **Filtrovanie výstupu**: Uložia sa len dáta pre cieľový rozsah `start_date` až `end_date`
4. **Po dňoch**: Výsledky sa uložia po dňoch ako pôvodne

## ✅ Výsledok

Po spustení budú mať všetky dáta:
- ✅ Kompletné ADX_14, DMP_14, DMN_14 indikátory
- ✅ Správne hodnoty na začiatku dní (nie NaN)
- ✅ Konzistentné features medzi rôznymi obdobiami
- ✅ Kompatibilita s existujúcimi simuláciami

## 🧪 Testovanie

Po prepočítaní môžeš otestovať:

```bash
# Test aprílových dát (predtým problematické)
python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-04-01 --end 2025-04-02

# Porovnanie s júlovými dátami
python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-07-01 --end 2025-07-02
```

## 📝 Poznámky

- **Lookback 30 dní** je obvykle dostačujúci pre väčšinu indikátorov
- **Batch spracovanie** rozdeľuje veľké rozsahy na menšie časti
- **Automatická detekcia** dostupných dátumov v raw data adresári
- **Graceful handling** chýbajúcich súborov a chýb

## 🆘 Riešenie problémov

### Chyba "Žiadne dáta nájdené":
- Skontroluj, či existuje `parquet_raw/ohlcv/1s/` alebo `parquet_raw/ohlcv/5m/`
- Skontroluj formát súborov (YYYY-MM-DD.parquet)

### Chyba "Cannot convert numpy.ndarray":
- Toto je problém s pandas/pyarrow verziami, nie s týmto scriptom
- Script by mal fungovať aj s problematickými súbormi

### Pomalé spracovanie:
- Zníž `--max-range-days` na menšiu hodnotu (napr. 7)
- Zníž `--lookback-days` ak nepotrebuješ dlhú históriu
