#!/bin/bash

# Jednotný script na prepočítanie vš<PERSON><PERSON><PERSON><PERSON> indikátorov s plnou históriou
# <PERSON><PERSON>ši problém s chýbajúcimi ADX, DMP, DMN indikátormi na začiatku dní

echo "🚀 Spúšťam prepočítanie všetk<PERSON>ch indikátorov s plnou históriou..."
echo ""

# Nastavenia
RAW_DATA_DIR="parquet_raw"
OUTPUT_DIR="parquet_processed"
LOOKBACK_DAYS=30

# Kontrola existencie potrebných súborov
if [ ! -f "recompute_all_features.py" ]; then
    echo "❌ Chyba: recompute_all_features.py neexistuje"
    exit 1
fi

if [ ! -f "batch_recompute_features.py" ]; then
    echo "❌ Chyba: batch_recompute_features.py neexistuje"
    exit 1
fi

if [ ! -d "$RAW_DATA_DIR" ]; then
    echo "❌ Chyba: Raw data adresár neexistuje: $RAW_DATA_DIR"
    exit 1
fi

# Vytvorenie output adresára ak neexistuje
mkdir -p "$OUTPUT_DIR"

echo "📁 Nastavenia:"
echo "   Raw data: $RAW_DATA_DIR"
echo "   Output: $OUTPUT_DIR"
echo "   Lookback: $LOOKBACK_DAYS dní"
echo ""

# Spustenie pre oba timeframes
echo "🔧 Spúšťam batch prepočítanie pre oba timeframes (1s a 5m)..."
echo ""

python batch_recompute_features.py \
    --preset both \
    --raw-data-dir "$RAW_DATA_DIR" \
    --output-dir "$OUTPUT_DIR" \
    --lookback-days $LOOKBACK_DAYS

# Kontrola výsledku
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 ÚSPEŠNE DOKONČENÉ!"
    echo ""
    echo "✅ Všetky indikátory (vrátane ADX, DMP, DMN) by mali byť teraz správne vypočítané"
    echo "✅ Indikátory na začiatku dní majú históriu z predchádzajúcich dní"
    echo ""
    echo "📊 Môžeš teraz spustiť simuláciu s úplnými dátami:"
    echo "   python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-04-01 --end 2025-04-30"
    echo ""
else
    echo ""
    echo "❌ CHYBA pri prepočítaní features!"
    echo "   Skontroluj logy vyššie pre detaily"
    exit 1
fi
