#!/usr/bin/env python3
"""
Jednotný script na prepočítanie všetký<PERSON> features s plnou históriou.
Rieši problém s chýbajúcimi indikátormi na začiatku dní tým, že načíta všetky dostupné dáta
a vypočíta indikátory s plnou históriou.
"""

import json
import logging
import pandas as pd
from pathlib import Path
from datetime import datetime, timezone, timedelta
import numpy as np
from indicators import calculate_and_merge_indicators
import warnings
import argparse
from typing import Dict, List, Tuple

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_all_available_dates(raw_data_dir: str, symbol: str, timeframe: str) -> List[str]:
    """
    Nájde všetky dostupné dátumy v raw data adresári.
    
    Args:
        raw_data_dir: <PERSON><PERSON><PERSON><PERSON> so surovými dátami
        symbol: Symbol (napr. XRPUSDC)
        timeframe: Timeframe (napr. 1s, 5m)
        
    Returns:
        List dátumov vo formáte YYYY-MM-DD
    """
    ohlcv_dir = Path(raw_data_dir) / "ohlcv" / timeframe
    if not ohlcv_dir.exists():
        logger.error(f"OHLCV adresár neexistuje: {ohlcv_dir}")
        return []
    
    dates = []
    for file in ohlcv_dir.glob("*.parquet"):
        try:
            # Predpokladáme formát súboru YYYY-MM-DD.parquet
            date_str = file.stem
            datetime.strptime(date_str, "%Y-%m-%d")  # Validácia formátu
            dates.append(date_str)
        except ValueError:
            continue
    
    return sorted(dates)

def load_extended_data_range(raw_data_dir: str, symbol: str, timeframe: str, 
                           target_start: str, target_end: str, 
                           lookback_days: int = 30) -> Dict:
    """
    Načíta dáta s rozšíreným rozsahom pre výpočet indikátorov.
    
    Args:
        raw_data_dir: Adresár so surovými dátami
        symbol: Symbol
        timeframe: Timeframe
        target_start: Cieľový začiatočný dátum
        target_end: Cieľový koncový dátum
        lookback_days: Počet dní histórie pred target_start
        
    Returns:
        Dictionary s načítanými dátami
    """
    # Rozšírenie rozsahu o lookback_days
    target_start_dt = datetime.strptime(target_start, "%Y-%m-%d")
    extended_start_dt = target_start_dt - timedelta(days=lookback_days)
    extended_start = extended_start_dt.strftime("%Y-%m-%d")
    
    logger.info(f"Načítavam dáta od {extended_start} do {target_end} (rozšírené o {lookback_days} dní)")
    
    # Nájdenie všetkých dostupných dátumov
    all_dates = find_all_available_dates(raw_data_dir, symbol, timeframe)
    
    # Filtrovanie dátumov v rozšírenom rozsahu
    relevant_dates = [d for d in all_dates if extended_start <= d <= target_end]
    
    if not relevant_dates:
        logger.error(f"Žiadne dáta pre obdobie {extended_start} - {target_end}")
        return {}
    
    logger.info(f"Našiel som {len(relevant_dates)} dní dát: {relevant_dates[0]} až {relevant_dates[-1]}")
    
    # Načítanie OHLCV dát
    raw_data_path = Path(raw_data_dir)
    data_dict = {}
    
    ohlcv_dir = raw_data_path / "ohlcv" / timeframe
    ohlcv_dfs = []
    
    for date in relevant_dates:
        file_path = ohlcv_dir / f"{date}.parquet"
        if file_path.exists():
            try:
                df = pd.read_parquet(file_path)
                if "timestamp" in df.columns:
                    df.set_index("timestamp", inplace=True)
                df.index = pd.to_datetime(df.index, utc=True)
                
                # Základná validácia
                if len(df) > 0:
                    ohlcv_dfs.append(df)
                    logger.debug(f"Načítaný {date}: {len(df)} riadkov")
                else:
                    logger.warning(f"Prázdny súbor: {file_path}")
            except Exception as e:
                logger.error(f"Chyba pri načítaní {file_path}: {e}")
        else:
            logger.warning(f"Súbor neexistuje: {file_path}")
    
    if ohlcv_dfs:
        combined_df = pd.concat(ohlcv_dfs).sort_index()
        # Odstránenie duplikátov
        combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
        data_dict[timeframe] = combined_df
        logger.info(f"Celkom načítaných {len(combined_df)} OHLCV riadkov")
    else:
        logger.error("Žiadne OHLCV dáta sa nepodarilo načítať")
        return {}
    
    # TODO: Načítanie orderbook a trades dát (ak potrebné)
    # Pre teraz sa sústredíme na OHLCV + indikátory
    
    return data_dict

def save_features_by_date(features_df: pd.DataFrame, output_dir: str, symbol: str, 
                         timeframe: str, target_start: str, target_end: str):
    """
    Uloží features po dňoch do parquet súborov.
    
    Args:
        features_df: DataFrame s features
        output_dir: Výstupný adresár
        symbol: Symbol
        timeframe: Timeframe
        target_start: Začiatočný dátum pre ukladanie
        target_end: Koncový dátum pre ukladanie
    """
    # Vytvorenie výstupného adresára
    output_symbol_dir = Path(output_dir) / symbol / timeframe
    output_symbol_dir.mkdir(parents=True, exist_ok=True)
    
    # Filtrovanie na cieľový rozsah
    target_start_dt = datetime.strptime(target_start, "%Y-%m-%d").replace(tzinfo=timezone.utc)
    target_end_dt = datetime.strptime(target_end, "%Y-%m-%d").replace(tzinfo=timezone.utc) + timedelta(days=1)
    
    mask = (features_df.index >= target_start_dt) & (features_df.index < target_end_dt)
    target_features_df = features_df[mask]
    
    if len(target_features_df) == 0:
        logger.warning(f"Žiadne features pre cieľový rozsah {target_start} - {target_end}")
        return
    
    logger.info(f"Ukladám {len(target_features_df)} riadkov features pre rozsah {target_start} - {target_end}")
    
    # Uloženie po dňoch
    features_df_grouped = target_features_df.groupby(target_features_df.index.date)
    
    saved_files = 0
    for date, day_df in features_df_grouped:
        output_file = output_symbol_dir / f"{date}.parquet"
        
        # Reset index to make timestamp a column (required by simulate_trading.py)
        day_df_with_timestamp = day_df.reset_index()
        day_df_with_timestamp.to_parquet(output_file, engine='pyarrow', index=False)
        
        logger.info(f"✅ Uložený {output_file} s {len(day_df_with_timestamp)} riadkami")
        saved_files += 1
    
    logger.info(f"🎉 Celkom uložených {saved_files} súborov")

def recompute_all_features(config_path: str, raw_data_dir: str, output_dir: str,
                          start_date: str, end_date: str, lookback_days: int = 30):
    """
    Prepočíta všetky features s plnou históriou.
    
    Args:
        config_path: Cesta k JSON konfiguračnému súboru
        raw_data_dir: Adresár so surovými dátami
        output_dir: Adresár pre výstupné parquet súbory
        start_date: Začiatočný dátum (YYYY-MM-DD)
        end_date: Koncový dátum (YYYY-MM-DD)
        lookback_days: Počet dní histórie pre indikátory
    """
    logger.info(f"🚀 Spúšťam prepočítanie features s plnou históriou")
    logger.info(f"   Cieľový rozsah: {start_date} až {end_date}")
    logger.info(f"   Lookback: {lookback_days} dní")
    
    # Načítanie konfigurácie
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    symbol = config["symbol"]
    primary_tf = config["primaryTimeframe"]
    
    logger.info(f"   Symbol: {symbol}, Timeframe: {primary_tf}")
    
    # Načítanie rozšírených dát
    data_dict = load_extended_data_range(
        raw_data_dir, symbol, primary_tf, start_date, end_date, lookback_days
    )
    
    if not data_dict:
        logger.error("❌ Nepodarilo sa načítať dáta")
        return
    
    # Výpočet features s plnou históriou
    logger.info("🔄 Začínam výpočet features s plnou históriou...")
    try:
        features_df, feature_names = calculate_and_merge_indicators(
            data=data_dict,
            cfg=config,
            skip_hmm=False
        )
        
        logger.info(f"✅ Vypočítané features: {len(feature_names)} stĺpcov, {len(features_df)} riadkov")
        logger.info(f"   Features: {feature_names[:10]}...")
        
        # Kontrola kľúčových indikátorov
        key_indicators = ['ADX_14', 'DMP_14', 'DMN_14', 'RSI_14', 'ATR_14']
        missing_indicators = [ind for ind in key_indicators if ind not in features_df.columns]
        present_indicators = [ind for ind in key_indicators if ind in features_df.columns]
        
        if present_indicators:
            logger.info(f"✅ Prítomné kľúčové indikátory: {present_indicators}")
        if missing_indicators:
            logger.warning(f"⚠️  Chýbajúce kľúčové indikátory: {missing_indicators}")
        
    except Exception as e:
        logger.error(f"❌ Chyba pri výpočte features: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Uloženie features po dňoch
    logger.info("💾 Ukladám features po dňoch...")
    save_features_by_date(features_df, output_dir, symbol, primary_tf, start_date, end_date)
    
    logger.info("🎉 Prepočítanie features dokončené!")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Prepočíta všetky features s plnou históriou")
    parser.add_argument("--config", required=True, help="Cesta k JSON config súboru")
    parser.add_argument("--raw-data-dir", required=True, help="Adresár so surovými dátami")
    parser.add_argument("--output-dir", required=True, help="Výstupný adresár pre features")
    parser.add_argument("--start", required=True, help="Začiatočný dátum (YYYY-MM-DD)")
    parser.add_argument("--end", required=True, help="Koncový dátum (YYYY-MM-DD)")
    parser.add_argument("--lookback-days", type=int, default=30, help="Počet dní histórie pre indikátory (default: 30)")
    
    args = parser.parse_args()
    
    recompute_all_features(
        config_path=args.config,
        raw_data_dir=args.raw_data_dir,
        output_dir=args.output_dir,
        start_date=args.start,
        end_date=args.end,
        lookback_days=args.lookback_days
    )
